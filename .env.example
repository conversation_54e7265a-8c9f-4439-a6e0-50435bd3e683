# ==================== Consul 配置 (必需) ====================
CONSUL_HOST=localhost
CONSUL_PORT=8500

# ==================== 服务配置 (必需) ====================
SERVICE_NAME=product-similarity
SERVICE_PORT=8000
SERVICE_TAGS=api,product,similarity
ENVIRONMENT=development

# ==================== 服务器配置 ====================
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO

# ==================== PostgreSQL 配置 ====================
PG_USER=lens
PG_PASSWORD=Ls.3956573
PG_DB=lens
PG_HOST=************
PG_PORT=5432

# ==================== Redis 配置 ====================
# 方式1: 分别配置各项参数
REDIS_HOST=************
REDIS_PORT=6379
REDIS_PASSWORD=ls3956573
REDIS_DB=5

# 方式2: 使用完整的 Redis URL（如果设置了 REDIS_URL，会优先使用）
# 无密码: redis://redis:6379/5
# 有密码: redis://:your_password@redis:6379/5
# 外部Redis: redis://:password@your-redis-host:6379/5
# REDIS_URL=redis://:ls3956573@************:6379/5

# ==================== OpenAI 配置 ====================
# 使用 TEXT_ENDPOINTS 配置多个模型端点
TEXT_ENDPOINTS=[{"url":"http://************:3000","api_key":"sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK","model":"deepseek/deepseek-chat-v3-0324:free","is_multimodal":false},{"url":"http://************:3000","api_key":"sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK","model":"deepseek-ai/DeepSeek-V3","is_multimodal":false}]

# 新格式：OPENAI_CREDENTIALS (备用配置)
# OPENAI_CREDENTIALS=[
# {"api_key":"sk-20wUPZL4VpAAPHTW1m6LKlC4jsrlwxLzko8Zbgskk176BVBc",
# "base_url":"http://************:3000/v1",
# "model":"glm-4v-flash"}]

# 多模态端点 (可选)
MM_ENDPOINTS=[]

# ==================== AI 模型配置 ====================
AI_TIMEOUT=120
AI_TEMPERATURE=0.1

# ==================== 业务配置 ====================
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30
CACHE_TTL=3600

# ==================== 开发环境示例 ====================
# 如果是开发环境，可以使用以下配置：

# # Consul (开发环境)
# CONSUL_HOST=localhost
# CONSUL_PORT=8500

# # 服务配置 (开发环境)
# SERVICE_NAME=product-similarity-dev
# SERVICE_TAGS=api,product,similarity,dev
# ENVIRONMENT=development

# # 数据库 (开发环境)
# PG_HOST=localhost
# PG_USER=postgres
# PG_PASSWORD=postgres
# PG_DB=product_similarity_dev

# # Redis (开发环境)
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=
# REDIS_DB=0

# # 日志级别 (开发环境)
# LOG_LEVEL=DEBUG

# ==================== Docker 环境示例 ====================
# 如果使用Docker Compose，可以使用以下配置：

# # Consul (Docker)
# CONSUL_HOST=consul
# CONSUL_PORT=8500

# # 数据库 (Docker)
# PG_HOST=postgres
# PG_USER=postgres
# PG_PASSWORD=postgres
# PG_DB=product_similarity

# # Redis (Docker)
# REDIS_HOST=redis
# REDIS_PORT=6379
# REDIS_PASSWORD=
# REDIS_DB=0

# ==================== 配置说明 ====================
# 
# 1. Consul配置：
#    - CONSUL_HOST: Consul服务器地址
#    - CONSUL_PORT: Consul服务器端口
#
# 2. 服务配置：
#    - SERVICE_NAME: 服务名称，用于Consul注册和路由
#    - SERVICE_PORT: 服务端口，固定为8000
#    - SERVICE_TAGS: 服务标签，至少包含'api'
#    - ENVIRONMENT: 运行环境 (development/staging/production)
#
# 3. 数据库配置：
#    - 支持PostgreSQL数据库
#    - 需要提供完整的连接信息
#
# 4. Redis配置：
#    - 支持两种配置方式
#    - 优先使用REDIS_URL，如果未设置则使用分别配置的参数
#
# 5. AI配置：
#    - 支持新旧两种格式
#    - 推荐使用TEXT_ENDPOINTS配置多个模型端点
#    - 备用格式使用OPENAI_CREDENTIALS
#    - 多模态端点使用MM_ENDPOINTS
#
# 6. 业务配置：
#    - MAX_CONCURRENT_REQUESTS: 最大并发请求数
#    - REQUEST_TIMEOUT: 请求超时时间（秒）
#    - CACHE_TTL: 缓存过期时间（秒）
#    - AI_TIMEOUT: AI模型调用超时时间（秒）
#    - AI_TEMPERATURE: AI模型温度参数
