# 微服务基础设施环境变量配置
# 复制此文件为 .env 并修改相应配置

# ==================== 数据库配置 ====================
POSTGRES_DB=microservices
POSTGRES_USER=admin
POSTGRES_PASSWORD=admin123

# 数据库连接URL（供业务服务使用）
DATABASE_URL=*****************************************/microservices

# ==================== Redis 配置 ====================
REDIS_PASSWORD=redis123

# Redis连接URL（供业务服务使用）
REDIS_URL=redis://:redis123@redis:6379/0

# ==================== Consul 配置 ====================
CONSUL_HOST=consul
CONSUL_PORT=8500

# ==================== 网关配置 ====================
NGINX_HOST=nginx-gateway
NGINX_PORT=80

# ==================== 服务发现配置 ====================
# 服务注册中心地址
SERVICE_REGISTRY=consul:8500

# ==================== 监控配置 ====================
# 健康检查间隔（秒）
HEALTH_CHECK_INTERVAL=15

# 服务超时时间（秒）
SERVICE_TIMEOUT=30

# ==================== 安全配置 ====================
# JWT密钥（如果需要认证）
JWT_SECRET=your-jwt-secret-key-here

# API密钥（如果需要API认证）
API_KEY=your-api-key-here

# ==================== 日志配置 ====================
LOG_LEVEL=INFO
LOG_FORMAT=json

# ==================== 环境标识 ====================
ENVIRONMENT=production
DATACENTER=dc1

# ==================== 网络配置 ====================
# 微服务网络名称
NETWORK_NAME=microservices

# ==================== 备份配置 ====================
# 数据备份目录
BACKUP_DIR=./backups

# 备份保留天数
BACKUP_RETENTION_DAYS=7

# ==================== 性能配置 ====================
# 最大连接数
MAX_CONNECTIONS=100

# 连接池大小
CONNECTION_POOL_SIZE=20

# 请求超时时间（秒）
REQUEST_TIMEOUT=30

# ==================== 开发配置 ====================
# 是否启用调试模式
DEBUG=false

# 是否启用热重载
HOT_RELOAD=false

# ==================== 扩展配置 ====================
# 自定义配置项可以在这里添加
# CUSTOM_CONFIG_1=value1
# CUSTOM_CONFIG_2=value2
