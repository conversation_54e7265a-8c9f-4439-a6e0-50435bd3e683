version: '3.9'

# 产品相似度业务服务
# 连接到已存在的微服务基础设施

networks:
  microservices:
    name: microservices
    external: true

services:
  # ==================== 产品相似度微服务 ====================
  product-similarity:
    build: .
    container_name: product-similarity-server
    hostname: product-similarity
    environment:
      # Consul配置 - 连接到主服务的Consul
      - CONSUL_HOST=${CONSUL_HOST:-consul}
      - CONSUL_PORT=${CONSUL_PORT:-8500}

      # 服务配置
      - SERVICE_NAME=product-similarity
      - SERVICE_PORT=${SERVICE_PORT:-8000}
      - SERVICE_TAGS=${SERVICE_TAGS:-api,product,similarity,business}
      - ENVIRONMENT=${ENVIRONMENT:-production}

      # 服务器配置
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=${LOG_LEVEL:-INFO}

      # 数据库配置 - 连接到主服务的数据库或外部数据库
      - PG_HOST=${PG_HOST:-postgres}
      - PG_PORT=${PG_PORT:-5432}
      - PG_USER=${PG_USER:-admin}
      - PG_PASSWORD=${PG_PASSWORD:-admin123}
      - PG_DB=${PG_DB:-microservices}

      # Redis配置 - 连接到主服务的Redis或外部Redis
      - REDIS_HOST=${REDIS_HOST:-redis}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis123}
      - REDIS_DB=${REDIS_DB:-0}

      # AI配置 - 从环境变量读取
      - TEXT_ENDPOINTS=${TEXT_ENDPOINTS}
      - OPENAI_CREDENTIALS=${OPENAI_CREDENTIALS}

      # 业务配置
      - MAX_CONCURRENT_REQUESTS=${MAX_CONCURRENT_REQUESTS:-10}
      - REQUEST_TIMEOUT=${REQUEST_TIMEOUT:-30}
      - CACHE_TTL=${CACHE_TTL:-3600}
      - AI_TIMEOUT=${AI_TIMEOUT:-120}
      - AI_TEMPERATURE=${AI_TEMPERATURE:-0.1}

    env_file:
      - .env
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8000/health"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    command: ["python", "run_consul_api.py"]

  # ==================== 可选：独立数据库（如果不使用主服务数据库）====================
  # product-similarity-postgres:
  #   image: postgres:16-alpine
  #   container_name: product-similarity-postgres
  #   hostname: product-similarity-postgres
  #   environment:
  #     - POSTGRES_DB=product_similarity
  #     - POSTGRES_USER=similarity_user
  #     - POSTGRES_PASSWORD=similarity_password
  #     - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
  #   volumes:
  #     - product_similarity_data:/var/lib/postgresql/data
  #     - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
  #   networks:
  #     - microservices
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U similarity_user -d product_similarity"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5
  #     start_period: 30s
  #   restart: unless-stopped

# volumes:
#   product_similarity_data:
#     driver: local

# ==================== 使用说明 ====================
#
# 1. 确保主服务（基础设施）正在运行：
#    docker-compose -f docker-compose.infrastructure.yml ps
#
# 2. 配置环境变量：
#    cp .env.business.example .env
#    # 编辑 .env 文件，配置数据库连接、AI端点等
#
# 3. 构建并启动业务服务：
#    docker-compose -f docker-compose.business.yml up --build -d
#
# 4. 查看服务状态：
#    docker-compose -f docker-compose.business.yml ps
#
# 5. 查看服务日志：
#    docker-compose -f docker-compose.business.yml logs -f product-similarity
#
# 6. 验证服务注册：
#    curl http://localhost:8500/v1/catalog/service/product-similarity
#
# 7. 通过网关访问服务：
#    curl http://localhost/api/product-similarity/health
#    curl http://localhost/api/product-similarity/info
#
# 8. 停止业务服务：
#    docker-compose -f docker-compose.business.yml down
#
# 9. 扩展服务实例（负载均衡测试）：
#    docker-compose -f docker-compose.business.yml up -d --scale product-similarity=3
#
# 10. 跨主机部署：
#     - 修改 .env 中的 CONSUL_HOST、PG_HOST、REDIS_HOST 为主服务IP
#     - 确保网络连通性
#     - 创建相同名称的外部网络：docker network create microservices
