# 产品相似度微服务

基于AI的产品相似度比较微服务，支持Consul服务发现、异步处理和高并发。

## 🚀 功能特性

- **产品信息管理**: 获取和缓存Wildberries产品信息
- **AI相似度比较**: 基于AI模型的产品相似度分析
- **批量处理**: 支持批量产品信息获取和相似度比较
- **服务发现**: 基于Consul的微服务注册和发现
- **异步处理**: 全异步架构，支持高并发
- **数据缓存**: PostgreSQL持久化缓存，Redis内存缓存
- **健康检查**: 完整的健康检查和监控
- **容器化**: Docker和Docker Compose支持

## 📁 项目结构

```
product_comparison/
├── src/
│   └── product_similarity/
│       ├── __init__.py
│       ├── api.py                  # FastAPI应用
│       ├── config.py               # 配置管理
│       ├── consul_service.py       # Consul集成
│       ├── crud.py                 # 数据库操作
│       ├── db.py                   # 数据库连接
│       ├── logging.py              # 日志配置
│       ├── models.py               # 数据模型
│       ├── schemas.py              # Pydantic模型
│       ├── service.py              # 业务逻辑
│       ├── service_client.py       # 服务客户端
│       ├── utils.py                # 工具函数
│       └── services/
│           ├── __init__.py
│           ├── product.py          # 产品服务
│           └── similarity.py       # 相似度服务
├── docker-compose.consul.yml       # Docker编排
├── Dockerfile                      # 容器配置
├── requirements.txt                # Python依赖
├── .env.example                    # 环境变量模板
├── run_consul_api.py              # 启动脚本
├── test_test_product_ids.py       # 测试脚本
├── test_product_ids.py            # 测试数据
└── README.md                      # 项目文档
```

## 🛠️ 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd product_comparison

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
nano .env
```

关键配置项：
```env
# 数据库配置
PG_HOST=************
PG_USER=lens
PG_PASSWORD=Ls.3956573
PG_DB=lens

# AI模型配置
TEXT_ENDPOINTS=[{"url":"http://************:3000","api_key":"sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK","model":"deepseek/deepseek-chat-v3-0324:free","is_multimodal":false},{"url":"http://************:3000","api_key":"sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK","model":"deepseek-ai/DeepSeek-V3","is_multimodal":false}]

# Consul配置
CONSUL_HOST=consul
CONSUL_PORT=8500
```

### 3. 启动服务

#### 方式1: 直接启动
```bash
python run_consul_api.py
```

#### 方式2: Docker Compose启动
```bash
# 启动所有服务（包括Consul、PostgreSQL、Redis）
docker-compose -f docker-compose.consul.yml up -d

# 查看服务状态
docker-compose -f docker-compose.consul.yml ps

# 查看日志
docker-compose -f docker-compose.consul.yml logs -f product-similarity
```

### 4. 验证服务

```bash
# 健康检查
curl http://localhost:8000/health

# 服务信息
curl http://localhost:8000/info

# API文档
open http://localhost:8000/docs
```

### 5. 运行测试

```bash
# 运行完整测试套件
python test_test_product_ids.py

# 指定测试目标
TEST_BASE_URL=http://localhost:8000 python test_test_product_ids.py
```

## 📚 API文档

### 核心端点

#### 健康检查
```http
GET /health
```

#### 服务信息
```http
GET /info
```

#### 获取产品信息
```http
GET /product/{product_id}?force_refresh=false
```

#### 批量获取产品信息
```http
POST /products/batch
Content-Type: application/json

{
  "product_ids": [123456, 789012],
  "force_refresh": false
}
```

#### 产品相似度比较
```http
POST /compare
Content-Type: application/json

{
  "product_id1": 123456,
  "product_id2": 789012,
  "mode": "text",
  "convert": false
}
```

#### 批量产品比较
```http
POST /compare/batch
Content-Type: application/json

{
  "product_pairs": [[123456, 789012], [345678, 901234]],
  "mode": "text",
  "max_concurrent": 5
}
```

#### 获取产品相似度列表
```http
GET /product/{product_id}/similarities?limit=10
```

#### 获取高相似度产品对
```http
GET /similarities/top?limit=20
```

### 响应格式

成功响应：
```json
{
  "status": "success",
  "data": {...},
  "message": ""
}
```

错误响应：
```json
{
  "status": "error",
  "message": "错误描述",
  "error_code": "ERROR_CODE"
}
```

## 🔧 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 | 必需 |
|--------|------|--------|------|
| `CONSUL_HOST` | Consul服务器地址 | consul | 是 |
| `CONSUL_PORT` | Consul服务器端口 | 8500 | 是 |
| `SERVICE_NAME` | 服务名称 | product-similarity | 是 |
| `SERVICE_PORT` | 服务端口 | 8000 | 是 |
| `PG_HOST` | PostgreSQL地址 | localhost | 是 |
| `PG_USER` | 数据库用户名 | postgres | 是 |
| `PG_PASSWORD` | 数据库密码 | - | 是 |
| `PG_DB` | 数据库名称 | - | 是 |
| `TEXT_ENDPOINTS` | AI模型配置 | [] | 是 |
| `REDIS_HOST` | Redis地址 | localhost | 否 |
| `LOG_LEVEL` | 日志级别 | INFO | 否 |

### AI模型配置

支持两种配置格式：

TEXT_ENDPOINTS格式（推荐，支持多个模型）：
```json
[
  {
    "url": "http://************:3000",
    "api_key": "sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK",
    "model": "deepseek/deepseek-chat-v3-0324:free",
    "is_multimodal": false
  },
  {
    "url": "http://************:3000",
    "api_key": "sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK",
    "model": "deepseek-ai/DeepSeek-V3",
    "is_multimodal": false
  }
]
```

OPENAI_CREDENTIALS格式（备用）：
```json
[{
  "api_key": "your-api-key",
  "base_url": "http://your-ai-service:3000/v1",
  "model": "your-model"
}]
```

## 🐳 Docker部署

### 构建镜像
```bash
docker build -t product-similarity:latest .
```

### 运行容器
```bash
docker run -d \
  --name product-similarity \
  -p 8000:8000 \
  --env-file .env \
  product-similarity:latest
```

### Docker Compose部署
```bash
# 启动完整环境
docker-compose -f docker-compose.consul.yml up -d

# 扩容服务实例
docker-compose -f docker-compose.consul.yml up -d --scale product-similarity=3
```

## 🔍 监控和日志

### 健康检查
- 端点: `/health`
- 检查项: 数据库、Consul、缓存、AI服务
- 状态: healthy/degraded/unhealthy

### 日志级别
- DEBUG: 详细调试信息
- INFO: 一般信息
- WARNING: 警告信息
- ERROR: 错误信息

### Consul监控
- UI地址: http://localhost:8500
- 服务注册状态
- 健康检查状态

## 🧪 测试

### 单元测试
```bash
pytest tests/ -v
```

### 集成测试
```bash
python test_test_product_ids.py
```

### 性能测试
```bash
# 使用Apache Bench
ab -n 1000 -c 10 http://localhost:8000/health

# 使用wrk
wrk -t12 -c400 -d30s http://localhost:8000/health
```

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
   - 检查环境变量配置
   - 确认数据库连接
   - 查看日志输出

2. **Consul连接失败**
   - 确认Consul服务运行
   - 检查网络连接
   - 验证端口配置

3. **AI模型调用失败**
   - 检查API密钥
   - 确认模型端点可用
   - 查看超时设置

4. **数据库连接问题**
   - 验证连接字符串
   - 检查数据库权限
   - 确认网络可达性

### 日志查看
```bash
# Docker环境
docker-compose -f docker-compose.consul.yml logs -f product-similarity

# 直接运行
tail -f logs/product-similarity.log
```

## 📈 性能优化

### 数据库优化
- 使用连接池
- 创建适当索引
- 定期清理过期数据

### 缓存策略
- PostgreSQL持久化缓存
- Redis内存缓存
- 多级缓存架构

### 并发控制
- 异步处理
- 连接池管理
- 限流和熔断

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 📞 支持

如有问题，请提交Issue或联系维护团队。
