#!/usr/bin/env python3
"""
测试图片描述功能
"""

from get_product_detail import get_product_detail
from image_description import test_image_description_api, get_image_description
from test_product_ids import nm_ids
import json


def test_single_image_description():
    """测试单张图片描述功能"""
    print("=== 测试单张图片描述功能 ===")
    
    # 使用示例中的图片URL
    test_url = "https://basket-12.wbcontent.net/vol1754/part175431/175431678/images/big/1.webp"
    
    description = get_image_description(test_url)
    
    if description:
        print("✅ 单张图片描述测试成功!")
        print(f"图片URL: {test_url}")
        print(f"描述: {description[:300]}...")
        return True
    else:
        print("❌ 单张图片描述测试失败!")
        return False


def test_product_with_image_description():
    """测试产品信息获取并添加图片描述"""
    print("\n=== 测试产品信息获取并添加图片描述 ===")
    
    # 使用真实的产品ID进行测试
    test_product_id = int(nm_ids[0])  # 使用第一个产品ID
    print(f"测试产品ID: {test_product_id}")
    
    try:
        # 获取产品详情（现在会自动添加图片描述）
        product_info = get_product_detail(test_product_id)
        
        if product_info:
            print("✅ 产品信息获取成功!")
            
            # 检查是否包含图片描述
            if "image_descriptions" in product_info:
                print("✅ 图片描述已添加!")
                
                # 显示图片描述信息
                image_descriptions = product_info["image_descriptions"]
                print(f"图片数量: {len(image_descriptions)}")
                
                for i, img_desc in enumerate(image_descriptions):
                    print(f"\n图片 {i+1}:")
                    print(f"  URL: {img_desc['url']}")
                    if img_desc['description']:
                        print(f"  描述: {img_desc['description'][:200]}...")
                    else:
                        print("  描述: 获取失败")
                
                # 显示合并的描述文本
                if "images_description_text" in product_info:
                    print(f"\n合并描述文本: {product_info['images_description_text'][:300]}...")
                
                return True
            else:
                print("❌ 图片描述未添加!")
                return False
        else:
            print("❌ 产品信息获取失败!")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False


def test_multiple_products():
    """测试多个产品的图片描述功能"""
    print("\n=== 测试多个产品的图片描述功能 ===")
    
    # 测试前3个产品
    test_ids = [int(nm_ids[i]) for i in range(min(3, len(nm_ids)))]
    
    success_count = 0
    
    for i, product_id in enumerate(test_ids):
        print(f"\n--- 测试产品 {i+1}: {product_id} ---")
        
        try:
            product_info = get_product_detail(product_id)
            
            if product_info and "image_descriptions" in product_info:
                print(f"✅ 产品 {product_id} 图片描述获取成功")
                
                # 显示基本信息
                print(f"  产品名称: {product_info.get('name', 'N/A')}")
                print(f"  图片数量: {len(product_info.get('image_descriptions', []))}")
                
                # 检查描述是否成功获取
                descriptions = product_info.get('image_descriptions', [])
                success_desc_count = len([d for d in descriptions if d.get('description')])
                print(f"  成功描述数量: {success_desc_count}/{len(descriptions)}")
                
                success_count += 1
            else:
                print(f"❌ 产品 {product_id} 图片描述获取失败")
                
        except Exception as e:
            print(f"❌ 产品 {product_id} 测试失败: {e}")
    
    print(f"\n总体测试结果: {success_count}/{len(test_ids)} 个产品成功")
    return success_count == len(test_ids)


def save_test_result():
    """保存测试结果到文件"""
    print("\n=== 保存测试结果 ===")
    
    # 获取一个产品的完整信息作为示例
    test_product_id = int(nm_ids[0])
    
    try:
        product_info = get_product_detail(test_product_id)
        
        if product_info:
            # 保存到JSON文件
            with open("test_product_with_image_desc.json", "w", encoding="utf-8") as f:
                json.dump(product_info, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 测试结果已保存到 test_product_with_image_desc.json")
            print(f"产品ID: {test_product_id}")
            print(f"文件大小: {len(json.dumps(product_info, ensure_ascii=False))} 字符")
            
            return True
        else:
            print("❌ 无法获取产品信息")
            return False
            
    except Exception as e:
        print(f"❌ 保存测试结果失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试图片描述功能...")
    
    # 1. 测试API连接
    api_test = test_image_description_api()
    
    if not api_test:
        print("❌ API连接测试失败，请检查图片描述服务是否正常运行")
        return
    
    # 2. 测试单张图片描述
    single_test = test_single_image_description()
    
    # 3. 测试产品信息获取并添加图片描述
    product_test = test_product_with_image_description()
    
    # 4. 测试多个产品
    multiple_test = test_multiple_products()
    
    # 5. 保存测试结果
    save_test = save_test_result()
    
    # 总结
    print("\n" + "="*50)
    print("测试总结:")
    print(f"API连接测试: {'✅ 通过' if api_test else '❌ 失败'}")
    print(f"单张图片测试: {'✅ 通过' if single_test else '❌ 失败'}")
    print(f"产品信息测试: {'✅ 通过' if product_test else '❌ 失败'}")
    print(f"多产品测试: {'✅ 通过' if multiple_test else '❌ 失败'}")
    print(f"结果保存测试: {'✅ 通过' if save_test else '❌ 失败'}")
    
    all_passed = all([api_test, single_test, product_test, multiple_test, save_test])
    print(f"\n整体测试结果: {'✅ 全部通过' if all_passed else '❌ 部分失败'}")


if __name__ == "__main__":
    main()
