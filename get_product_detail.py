import requests
from typing import Optional, Dict, Any,List
import concurrent.futures
import time
from image_description import add_image_descriptions_to_product
def _parse_product_url(product_id, product_img_num=1):
    product_id = int(product_id)
    short_id = product_id // 100000

    basket = ''
    if short_id <= 3486:
        if 0 <= short_id <= 143:
            basket = '01'
        elif 144 <= short_id <= 287:
            basket = '02'
        elif 288 <= short_id <= 431:
            basket = '03'
        elif 432 <= short_id <= 719:
            basket = '04'
        elif 720 <= short_id <= 1007:
            basket = '05'
        elif 1008 <= short_id <= 1061:
            basket = '06'
        elif 1062 <= short_id <= 1115:
            basket = '07'
        elif 1116 <= short_id <= 1169:
            basket = '08'
        elif 1170 <= short_id <= 1313:
            basket = '09'
        elif 1314 <= short_id <= 1601:
            basket = '10'
        elif 1602 <= short_id <= 1655:
            basket = '11'
        elif 1656 <= short_id <= 1919:
            basket = '12'
        elif 1920 <= short_id <= 2045:
            basket = '13'
        elif 2046 <= short_id <= 2189:
            basket = '14'
        elif 2190 <= short_id <= 2405:
            basket = '15'
        elif 2406 <= short_id <= 2621:
            basket = '16'
        elif 2622 <= short_id <= 2837:
            basket = '17'
        elif 2838 <= short_id <= 3053:
            basket = '18'
        elif 3054 <= short_id <= 3270:
            basket = '19'
        elif 3271 <= short_id <= 3486:
            basket = '20'
    else:
        delta = short_id - 3487
        basket_num = 21 + (delta // 216)
        basket = str(basket_num).zfill(2)

    part = product_id // 1000
    product_url = f"https://basket-{basket}.wbbasket.ru/vol{short_id}/part{part}/{product_id}/info/ru/card.json"

    parse_product_urls = [
        f"https://basket-{basket}.wbbasket.ru/vol{short_id}/part{part}/{product_id}/images/big/{i}.webp"
        for i in range(1, int(product_img_num) + 1)
    ]

    return {
        "parse_product_urls": parse_product_urls,
        "product_url": product_url
    }

def get_product_detail(product_id: int) -> Optional[Dict[str, Any]]:
    product_data = _parse_product_url(product_id, product_img_num=5)
    url = product_data['product_url']
    img_urls = product_data['parse_product_urls']

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Referer": f"https://www.wildberries.ru/catalog/{product_id}/detail.aspx",
        "Origin": "https://www.wildberries.ru",
        "Sec-Fetch-Site": "same-site",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Dest": "empty",
        "Accept-Language": "zh-CN,zh;q=0.9"
    }

    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        product_info = response.json()
        product_info["product_img_urls"] = img_urls

        # === 清洗无效数据，格式化product_info，压缩最小字符 ===
        keys_to_remove = ['colors', 'full_colors', 'selling', 'media', 'data', 'slug', 'certificate']
        for key in keys_to_remove:
            product_info.pop(key, None)  # 安全删除键

        # 处理 options 字段为扁平结构
        options = {}
        for option in product_info.get('options', []):
            if 'name' in option and 'value' in option:
                options[option['name']] = option['value']
        product_info['options'] = options

        # 处理 grouped_options 字段为扁平结构
        grouped_options_child = {}
        for group in product_info.get('grouped_options', []):
            for option in group.get('options', []):
                if 'name' in option and 'value' in option:
                    grouped_options_child[option['name']] = option['value']
        product_info['grouped_options'] = grouped_options_child

        # 仅保留第一张图片
        product_info['product_img_urls'] = product_info.get('product_img_urls', [])[:1]

        # 添加图片描述信息
        product_info = add_image_descriptions_to_product(product_info)

        return product_info

    except Exception as e:
        print(f"请求失败: {e}")
        return None