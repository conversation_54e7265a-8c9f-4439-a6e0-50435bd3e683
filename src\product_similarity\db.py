import asyncpg
from typing import Optional, Callable, Awaitable, Any
from functools import wraps
import asyncio

from .config import settings
from .models import ALL_TABLES_SQL
from .logging import logger, log_success, log_error, log_warning

# 全局连接池
_pool: Optional[asyncpg.Pool] = None

async def init_pool() -> asyncpg.Pool:
    """初始化数据库连接池"""
    global _pool
    if _pool is None:
        try:
            logger.info(f"正在连接数据库: {settings.PG_HOST}:{settings.PG_PORT}/{settings.PG_DB}")
            _pool = await asyncpg.create_pool(
                dsn=settings.PG_DSN,
                min_size=2,
                max_size=10,
                command_timeout=60,
                server_settings={
                    'jit': 'off'  # 禁用JIT以提高连接速度
                }
            )
            
            # 初始化数据库表
            await init_database()
            log_success("数据库连接池初始化成功")
            
        except Exception as e:
            log_error("数据库连接池初始化失败", error=e)
            raise
    
    return _pool

async def close_pool():
    """关闭数据库连接池"""
    global _pool
    if _pool:
        await _pool.close()
        _pool = None
        log_success("数据库连接池已关闭")

async def get_pool() -> asyncpg.Pool:
    """获取数据库连接池"""
    if _pool is None:
        return await init_pool()
    return _pool

async def init_database():
    """初始化数据库表和视图"""
    pool = await get_pool()
    async with pool.acquire() as conn:
        try:
            # 执行所有建表SQL
            for sql in ALL_TABLES_SQL:
                if sql.strip():  # 跳过空SQL
                    await conn.execute(sql)
            
            log_success("数据库表初始化完成")
            
        except Exception as e:
            log_error("数据库表初始化失败", error=e)
            raise

def with_connection(func: Callable) -> Callable:
    """
    数据库连接装饰器
    自动获取和释放数据库连接
    
    使用方式:
    @with_connection
    async def my_function(conn, other_params):
        # conn 是 asyncpg.Connection 对象
        result = await conn.fetchrow("SELECT * FROM table")
        return result
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        pool = await get_pool()
        async with pool.acquire() as conn:
            return await func(conn, *args, **kwargs)
    return wrapper

def with_transaction(func: Callable) -> Callable:
    """
    数据库事务装饰器
    自动处理事务的开始、提交和回滚
    
    使用方式:
    @with_transaction
    async def my_function(conn, other_params):
        # 在事务中执行操作
        await conn.execute("INSERT INTO table VALUES ($1)", value)
        await conn.execute("UPDATE table SET field = $1", new_value)
        # 如果没有异常，事务会自动提交
        # 如果有异常，事务会自动回滚
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        pool = await get_pool()
        async with pool.acquire() as conn:
            async with conn.transaction():
                return await func(conn, *args, **kwargs)
    return wrapper

async def execute_query(query: str, *args) -> Any:
    """执行查询语句"""
    pool = await get_pool()
    async with pool.acquire() as conn:
        return await conn.execute(query, *args)

async def fetch_one(query: str, *args) -> Optional[asyncpg.Record]:
    """获取单条记录"""
    pool = await get_pool()
    async with pool.acquire() as conn:
        return await conn.fetchrow(query, *args)

async def fetch_all(query: str, *args) -> list[asyncpg.Record]:
    """获取多条记录"""
    pool = await get_pool()
    async with pool.acquire() as conn:
        return await conn.fetch(query, *args)

async def fetch_val(query: str, *args) -> Any:
    """获取单个值"""
    pool = await get_pool()
    async with pool.acquire() as conn:
        return await conn.fetchval(query, *args)

async def health_check() -> dict:
    """数据库健康检查"""
    try:
        pool = await get_pool()
        async with pool.acquire() as conn:
            # 检查连接
            result = await conn.fetchval("SELECT 1")
            if result != 1:
                return {"status": "error", "message": "数据库查询结果异常"}
            
            # 检查表是否存在
            tables_exist = await conn.fetchval("""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = 'pj_similar'
            """)
            
            if tables_exist == 0:
                return {"status": "warning", "message": "数据库表不存在"}
            
            # 检查连接池状态
            pool_info = {
                "size": pool.get_size(),
                "min_size": pool.get_min_size(),
                "max_size": pool.get_max_size(),
                "idle_size": pool.get_idle_size()
            }
            
            return {
                "status": "healthy",
                "message": "数据库连接正常",
                "pool_info": pool_info,
                "tables_count": tables_exist
            }
            
    except Exception as e:
        log_error("数据库健康检查失败", error=e)
        return {"status": "error", "message": str(e)}

# 数据库统计信息
async def get_database_stats() -> dict:
    """获取数据库统计信息"""
    try:
        pool = await get_pool()
        async with pool.acquire() as conn:
            # 获取产品统计
            product_stats = await conn.fetchrow("SELECT * FROM pj_similar.product_stats")
            
            # 获取相似度统计
            similarity_stats = await conn.fetchrow("SELECT * FROM pj_similar.similarity_stats")
            
            # 获取热门产品（前10个）
            popular_products = await conn.fetch("""
                SELECT * FROM pj_similar.popular_products 
                LIMIT 10
            """)
            
            return {
                "product_stats": dict(product_stats) if product_stats else {},
                "similarity_stats": dict(similarity_stats) if similarity_stats else {},
                "popular_products": [dict(row) for row in popular_products]
            }
            
    except Exception as e:
        log_error("获取数据库统计信息失败", error=e)
        return {"error": str(e)}
