version: '3.9'

# 微服务基础设施 - 主服务
# 包含：Consul服务发现、Nginx网关、Redis缓存、PostgreSQL数据库

networks:
  microservices:
    name: microservices
    driver: bridge

volumes:
  consul_data:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local

services:
  # ==================== Consul 服务发现 ====================
  consul-server:
    image: consul:1.15
    container_name: consul-server
    hostname: consul
    command: >
      consul agent -server -bootstrap-expect=1 -datacenter=dc1 -data-dir=/consul/data
      -bind=0.0.0.0 -client=0.0.0.0 -retry-join=consul -ui -log-level=INFO
    ports:
      - "8500:8500"    # HTTP API
      - "8600:8600/udp" # DNS
    volumes:
      - consul_data:/consul/data
    networks:
      - microservices
    environment:
      - CONSUL_BIND_INTERFACE=eth0
    healthcheck:
      test: ["CMD", "consul", "members"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped

  # ==================== Consul Template ====================
  consul-template:
    image: hashicorp/consul-template:0.32.0
    container_name: consul-template
    hostname: consul-template
    depends_on:
      consul-server:
        condition: service_healthy
    volumes:
      - ./nginx/templates:/templates:ro
      - ./nginx/conf.d:/etc/nginx/conf.d
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - microservices
    command: >
      consul-template
      -consul-addr=consul:8500
      -template="/templates/services.conf.tpl:/etc/nginx/conf.d/services.conf:docker kill -s HUP nginx-gateway"
      -wait=5s:30s
      -log-level=info
    restart: unless-stopped

  # ==================== Nginx 网关 ====================
  nginx-gateway:
    image: nginx:1.25-alpine
    container_name: nginx-gateway
    hostname: nginx-gateway
    depends_on:
      consul-server:
        condition: service_healthy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/gateway/status"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped

  # ==================== PostgreSQL 数据库 ====================
  postgres-db:
    image: postgres:16-alpine
    container_name: postgres-db
    hostname: postgres
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-microservices}
      - POSTGRES_USER=${POSTGRES_USER:-admin}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-admin123}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    networks:
      - microservices
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-admin} -d ${POSTGRES_DB:-microservices}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped

  # ==================== Redis 缓存 ====================
  redis-cache:
    image: redis:7-alpine
    container_name: redis-cache
    hostname: redis
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --requirepass ${REDIS_PASSWORD:-redis123}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped

  # ==================== Consul UI 代理 ====================
  consul-ui-proxy:
    image: nginx:1.25-alpine
    container_name: consul-ui-proxy
    hostname: consul-ui
    depends_on:
      consul-server:
        condition: service_healthy
    ports:
      - "8501:80"
    volumes:
      - ./nginx/consul-ui.conf:/etc/nginx/conf.d/default.conf:ro
    networks:
      - microservices
    restart: unless-stopped

# ==================== 使用说明 ====================
#
# 1. 启动基础设施：
#    docker-compose -f docker-compose.infrastructure.yml up -d
#
# 2. 查看服务状态：
#    docker-compose -f docker-compose.infrastructure.yml ps
#
# 3. 查看日志：
#    docker-compose -f docker-compose.infrastructure.yml logs -f
#
# 4. 停止基础设施：
#    docker-compose -f docker-compose.infrastructure.yml down
#
# 5. 访问服务：
#    - Consul UI: http://localhost:8500 或 http://localhost:8501
#    - 网关状态: http://localhost/gateway/status
#    - PostgreSQL: localhost:5432
#    - Redis: localhost:6379
#
# 6. 环境变量配置：
#    - 复制 .env.infrastructure.example 到 .env
#    - 修改数据库和Redis密码
#
# 7. 数据持久化：
#    - PostgreSQL: postgres_data volume
#    - Redis: redis_data volume  
#    - Consul: consul_data volume
#
# 8. 网络配置：
#    - 网络名称: microservices
#    - 业务服务需要连接到此网络
